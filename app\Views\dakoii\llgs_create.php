<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> Add New LLG</h5>
                    <a href="<?= base_url('location-management/provinces') ?>" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to Provinces
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('location-management/llgs', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="form-group">
                            <label for="province_id">Province <span class="text-danger">*</span></label>
                            <select class="form-control" id="province_id" name="province_id" required>
                                <option value="">Select Province</option>
                                <?php foreach ($provinces as $prov): ?>
                                    <option value="<?= $prov['id'] ?>" 
                                            <?= (isset($province) && $province['id'] == $prov['id']) ? 'selected' : '' ?>
                                            <?= old('province_id') == $prov['id'] ? 'selected' : '' ?>>
                                        <?= esc($prov['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a province.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="district_id">District <span class="text-danger">*</span></label>
                            <select class="form-control" id="district_id" name="district_id" required>
                                <option value="">Select District</option>
                                <?php if (isset($district)): ?>
                                    <option value="<?= $district['id'] ?>" selected>
                                        <?= esc($district['name']) ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a district.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="name">LLG Name <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?= old('name') ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid LLG name.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="llgcode">LLG Code <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="llgcode" 
                                   name="llgcode" 
                                   value="<?= old('llgcode') ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid LLG code.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="json_id">JSON ID</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="json_id" 
                                   name="json_id" 
                                   value="<?= old('json_id') ?>">
                            <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                        </div>

                        <!-- Hidden country field -->
                        <input type="hidden" name="country_id" value="<?= $set_country['id'] ?>">

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save LLG
                            </button>
                            <a href="<?= base_url('location-management/provinces') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Country:</strong> <?= esc($set_country['name']) ?></p>
                    <?php if (isset($province)): ?>
                        <p><strong>Selected Province:</strong> <?= esc($province['name']) ?></p>
                    <?php endif; ?>
                    <?php if (isset($district)): ?>
                        <p><strong>Selected District:</strong> <?= esc($district['name']) ?></p>
                    <?php endif; ?>
                    <hr>
                    <h6>Guidelines:</h6>
                    <ul class="small">
                        <li>LLG name must be at least 3 characters long</li>
                        <li>LLG code must be unique</li>
                        <li>Must belong to a district and province</li>
                        <li>JSON ID is optional and used for mapping purposes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Dynamic district loading based on province selection
document.getElementById('province_id').addEventListener('change', function() {
    const provinceId = this.value;
    const districtSelect = document.getElementById('district_id');
    
    // Clear district options
    districtSelect.innerHTML = '<option value="">Select District</option>';
    
    if (provinceId) {
        // Fetch districts for selected province
        fetch(`<?= base_url('location-management/get-districts/') ?>${provinceId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(district => {
                    const option = document.createElement('option');
                    option.value = district.id;
                    option.textContent = district.name;
                    districtSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching districts:', error));
    }
});
</script>

<?= $this->endSection(); ?>

<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> Edit Ward</h5>
                    <a href="<?= base_url('location-management/wards/' . $ward['llg_id']) ?>" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to Wards
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('location-management/wards/' . $ward['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="form-group">
                            <label for="province_id">Province <span class="text-danger">*</span></label>
                            <select class="form-control" id="province_id" name="province_id" required>
                                <option value="">Select Province</option>
                                <?php foreach ($provinces as $prov): ?>
                                    <option value="<?= $prov['id'] ?>" 
                                            <?= old('province_id', $ward['province_id']) == $prov['id'] ? 'selected' : '' ?>>
                                        <?= esc($prov['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a province.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="district_id">District <span class="text-danger">*</span></label>
                            <select class="form-control" id="district_id" name="district_id" required>
                                <option value="">Select District</option>
                                <option value="<?= $district['id'] ?>" selected>
                                    <?= esc($district['name']) ?>
                                </option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a district.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="llg_id">LLG <span class="text-danger">*</span></label>
                            <select class="form-control" id="llg_id" name="llg_id" required>
                                <option value="">Select LLG</option>
                                <option value="<?= $llg['id'] ?>" selected>
                                    <?= esc($llg['name']) ?>
                                </option>
                            </select>
                            <div class="invalid-feedback">
                                Please select an LLG.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="name">Ward Name <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?= old('name', $ward['name']) ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid ward name.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="wardcode">Ward Code <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="wardcode" 
                                   name="wardcode" 
                                   value="<?= old('wardcode', $ward['wardcode']) ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid ward code.
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update Ward
                            </button>
                            <a href="<?= base_url('location-management/wards/' . $ward['llg_id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Country:</strong> <?= esc($set_country['name']) ?></p>
                    <p><strong>Current Province:</strong> <?= esc($province['name']) ?></p>
                    <p><strong>Current District:</strong> <?= esc($district['name']) ?></p>
                    <p><strong>Current LLG:</strong> <?= esc($llg['name']) ?></p>
                    <p><strong>Ward ID:</strong> <?= $ward['id'] ?></p>
                    <p><strong>Created:</strong> <?= date('M d, Y', strtotime($ward['created_at'])) ?></p>
                    <hr>
                    <h6>Guidelines:</h6>
                    <ul class="small">
                        <li>Ward name must be at least 3 characters long</li>
                        <li>Ward code must be unique</li>
                        <li>Must belong to an LLG, district, and province</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Dynamic district loading based on province selection
document.getElementById('province_id').addEventListener('change', function() {
    const provinceId = this.value;
    const districtSelect = document.getElementById('district_id');
    const llgSelect = document.getElementById('llg_id');
    
    // Clear district and LLG options
    districtSelect.innerHTML = '<option value="">Select District</option>';
    llgSelect.innerHTML = '<option value="">Select LLG</option>';
    
    if (provinceId) {
        // Fetch districts for selected province
        fetch(`<?= base_url('location-management/get-districts/') ?>${provinceId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(district => {
                    const option = document.createElement('option');
                    option.value = district.id;
                    option.textContent = district.name;
                    // Maintain current selection if editing
                    if (district.id == <?= $ward['district_id'] ?>) {
                        option.selected = true;
                    }
                    districtSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching districts:', error));
    }
});

// Dynamic LLG loading based on district selection
document.getElementById('district_id').addEventListener('change', function() {
    const districtId = this.value;
    const llgSelect = document.getElementById('llg_id');
    
    // Clear LLG options
    llgSelect.innerHTML = '<option value="">Select LLG</option>';
    
    if (districtId) {
        // Note: You'll need to create a getLLGs endpoint similar to getDistricts
        // For now, this is a placeholder
        console.log('District selected:', districtId);
        // TODO: Implement LLG fetching endpoint
    }
});
</script>

<?= $this->endSection(); ?>

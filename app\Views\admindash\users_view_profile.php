<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">User Profile</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>users">Users</a></li>
                    <li class="breadcrumb-item active">Profile</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4">
                <!-- Profile Image -->
                <div class="card card-success card-outline">
                    <div class="card-body box-profile">
                        <div class="text-center">
                            <img class="profile-user-img img-fluid img-circle"
                                 src="<?= base_url() ?>/public/assets/system_img/no-users-img.png"
                                 alt="User profile picture">
                        </div>

                        <h3 class="profile-username text-center"><?= esc($user['name']) ?></h3>
                        <p class="text-muted text-center"><?= esc($user['position']) ?></p>

                        <ul class="list-group list-group-unbordered mb-3">
                            <li class="list-group-item">
                                <b>User ID</b> <a class="float-right"><?= esc($user['id']) ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Role</b>
                                <span class="float-right badge badge-<?= $user['role'] == 'admin' ? 'danger' : 'success' ?>">
                                    <?= esc(ucfirst($user['role'])) ?>
                                </span>
                            </li>
                            <li class="list-group-item">
                                <b>Status</b>
                                <span class="float-right badge badge-<?= $user['status'] ? 'success' : 'danger' ?>">
                                    <?= $user['status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </li>
                        </ul>

                        <button type="button" class="btn btn-success btn-block"
                                onclick="editUser(<?= esc(json_encode($user)) ?>)">
                            <i class="fas fa-edit"></i> Edit Profile
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <!-- District Permissions -->
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">District Permissions</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addDistrictModal">
                                <i class="fas fa-plus"></i> Add District
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>District</th>
                                    <th>Default</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(isset($user_districts) && is_array($user_districts)): ?>
                                    <?php foreach($user_districts as $district): ?>
                                    <tr>
                                        <td><?= esc($district['districtcode']) ?> - <?= esc($district['district_name']) ?></td>
                                        <td>
                                            <span class="badge badge-<?= $district['default_district'] ? 'success' : 'secondary' ?>">
                                                <?= $district['default_district'] ? 'Yes' : 'No' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-primary"
                                                    onclick="setDefaultDistrict(<?= $district['id'] ?>)">
                                                <i class="fas fa-check"></i> Set Default
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="removeDistrict(<?= $district['id'] ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">No districts assigned</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Activity Timeline -->
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">Activity Timeline</h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <!-- Account Creation -->
                            <div>
                                <i class="fas fa-user bg-success"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> <?= date('d M Y', strtotime($user['created_at'])) ?>
                                    </span>
                                    <h3 class="timeline-header">Account Created</h3>
                                    <div class="timeline-body">
                                        Account was created by <?= esc($user['created_by']) ?>
                                    </div>
                                </div>
                            </div>

                            <?php if($user['updated_at'] && $user['updated_by']): ?>
                            <!-- Last Update -->
                            <div>
                                <i class="fas fa-edit bg-info"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> <?= date('d M Y', strtotime($user['updated_at'])) ?>
                                    </span>
                                    <h3 class="timeline-header">Last Update</h3>
                                    <div class="timeline-body">
                                        Profile was updated by <?= esc($user['updated_by']) ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('users/update_user') ?>
            <input type="hidden" name="id" id="edit_id">
            <div class="modal-body">
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" class="form-control" name="email" id="edit_email" required>
                    <small id="edit_emailFeedback" class="form-text"></small>
                </div>
                <div class="form-group">
                    <label>Phone</label>
                    <input type="text" class="form-control" name="phone" id="edit_phone">
                </div>
                <div class="form-group">
                    <label>Full Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>Position</label>
                    <input type="text" class="form-control" name="position" id="edit_position" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_role" required>
                        <option value="admin">Admin</option>
                        <option value="supervisor">Supervisor</option>
                        <option value="user">User</option>
                        <option value="guest">Guest</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select class="form-control" name="status" id="edit_status">
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>New Password (leave blank to keep current)</label>
                    <input type="password" class="form-control" name="password">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Update User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Add District Modal -->
<div class="modal fade" id="addDistrictModal" tabindex="-1" role="dialog" aria-labelledby="addDistrictModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="addDistrictModalLabel">Add District Permission</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('users/add_district_permission/' . $user['id']) ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>District</label>
                    <select class="form-control select2" name="district_id" required>
                        <option value="">Select District</option>
                        <?php if(isset($available_districts) && is_array($available_districts)): ?>
                            <?php foreach($available_districts as $district): ?>
                                <option value="<?= $district['id'] ?>"><?= esc($district['districtcode']) ?> - <?= esc($district['name']) ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="defaultDistrict" name="default_district" value="1">
                        <label class="custom-control-label" for="defaultDistrict">Set as default district</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Add District</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
function editUser(user) {
    $('#edit_id').val(user.id);
    $('#edit_email').val(user.email);
    $('#edit_phone').val(user.phone);
    $('#edit_name').val(user.name);
    $('#edit_position').val(user.position);
    $('#edit_role').val(user.role);
    $('#edit_status').val(user.status);
    $('#editUserModal').modal('show');
}

$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%',
        allowClear: true,
        placeholder: 'Select District',
        dropdownParent: $('#addDistrictModal'),
        language: {
            noResults: function() {
                return "No districts found";
            }
        }
    });
});

function setDefaultDistrict(permissionId) {
    if(confirm('Set this as the default district?')) {
        window.location.href = '<?= base_url() ?>users/set_default_district/' + permissionId;
    }
}

function removeDistrict(permissionId) {
    if(confirm('Are you sure you want to remove this district permission?')) {
        window.location.href = '<?= base_url() ?>users/remove_district_permission/' + permissionId;
    }
}
</script>

<style>
.profile-user-img {
    width: 100px;
    height: 100px;
}
.timeline {
    margin: 0;
    padding: 0;
    position: relative;
}
.timeline:before {
    background: #dee2e6;
    bottom: 0;
    content: "";
    left: 31px;
    margin: 0;
    position: absolute;
    top: 0;
    width: 4px;
}
.timeline > div {
    margin-bottom: 15px;
    margin-right: 10px;
    position: relative;
}
.timeline > div > .timeline-item {
    margin-left: 60px;
    margin-right: 15px;
}
.timeline > div > .fa,
.timeline > div > .fas {
    background: #adb5bd;
    border-radius: 50%;
    font-size: 1rem;
    height: 30px;
    left: 18px;
    line-height: 30px;
    position: absolute;
    text-align: center;
    top: 0;
    width: 30px;
    color: #fff;
}
.timeline > div > .bg-success {
    background-color: #28a745!important;
}
.timeline > div > .bg-info {
    background-color: #17a2b8!important;
}
.timeline-item {
    background: #fff;
    border-radius: 3px;
    margin-top: 0;
    padding: 0;
    position: relative;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
}
.timeline-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
    color: #495057;
    font-size: 16px;
    line-height: 1.1;
    margin: 0;
    padding: 10px;
}
.timeline-body {
    padding: 10px;
}
.time {
    color: #999;
    float: right;
    padding: 10px;
    font-size: 12px;
}
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(2.25rem + 2px) !important;
}
</style>

<?= $this->endSection() ?>
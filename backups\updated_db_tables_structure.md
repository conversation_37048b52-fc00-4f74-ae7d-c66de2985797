Showing create queries
Tables
Table	Create table
adx_country	CREATE TABLE `adx_country` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `code` varchar(2) NOT NULL,
 `created_at` datetime NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_crops	CREATE TABLE `adx_crops` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `crop_name` varchar(255) NOT NULL,
 `crop_icon` varchar(255) NOT NULL,
 `crop_color_code` varchar(7) NOT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `updated_by` int(11) NOT NULL,
 `updated_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_district	CREATE TABLE `adx_district` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `districtcode` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `json_id` varchar(50) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_education	CREATE TABLE `adx_education` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `icon` varchar(255) DEFAULT NULL,
 `color_code` varchar(20) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_fertilizers	CREATE TABLE `adx_fertilizers` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `icon` varchar(255) DEFAULT NULL,
 `color_code` varchar(20) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_infections	CREATE TABLE `adx_infections` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `icon` varchar(255) DEFAULT NULL,
 `color_code` varchar(20) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_livestock	CREATE TABLE `adx_livestock` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `icon` varchar(255) DEFAULT NULL,
 `color_code` varchar(50) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` tinyint(1) DEFAULT 1,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_llg	CREATE TABLE `adx_llg` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `llgcode` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `json_id` varchar(50) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_pesticides	CREATE TABLE `adx_pesticides` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `icon` varchar(255) DEFAULT NULL,
 `color_code` varchar(20) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_province	CREATE TABLE `adx_province` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `provincecode` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `country_id` int(11) NOT NULL,
 `json_id` varchar(50) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_ward	CREATE TABLE `adx_ward` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `wardcode` int(11) NOT NULL,
 `name` varchar(255) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
climate_focus	CREATE TABLE `climate_focus` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) DEFAULT NULL,
 `gps` varchar(100) NOT NULL,
 `location` varchar(255) NOT NULL,
 `remarks` text DEFAULT NULL,
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_blocks	CREATE TABLE `crops_farm_blocks` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `farmer_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `block_code` varchar(50) NOT NULL,
 `org_id` int(11) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `ward_id` int(11) NOT NULL,
 `village` varchar(100) NOT NULL,
 `block_site` varchar(200) NOT NULL,
 `lon` varchar(50) DEFAULT NULL,
 `lat` varchar(50) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `status` varchar(50) NOT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime NOT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_block_files	CREATE TABLE `crops_farm_block_files` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `farm_block_id` int(11) NOT NULL,
 `file_caption` varchar(255) NOT NULL,
 `file_path` varchar(500) NOT NULL,
 `uploaded_by` int(11) NOT NULL,
 `uploaded_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_crops_data	CREATE TABLE `crops_farm_crops_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `action_type` enum('add','remove') NOT NULL,
 `action_reason` varchar(100) NOT NULL,
 `number_of_plants` int(11) NOT NULL,
 `breed` varchar(255) DEFAULT NULL,
 `action_date` date NOT NULL,
 `hectares` decimal(10,2) NOT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime NOT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `status` enum('active','inactive','deleted') NOT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_disease_data	CREATE TABLE `crops_farm_disease_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `disease_type` varchar(255) NOT NULL,
 `disease_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `action_reason` text DEFAULT NULL,
 `number_of_plants` int(11) DEFAULT 0,
 `breed` varchar(255) DEFAULT NULL,
 `action_date` datetime DEFAULT NULL,
 `hectares` decimal(10,2) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT 'active',
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_fertilizer_data	CREATE TABLE `crops_farm_fertilizer_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `fertilizer_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `name` varchar(255) DEFAULT NULL,
 `brand` varchar(255) DEFAULT NULL,
 `unit_of_measure` varchar(50) DEFAULT NULL,
 `unit` decimal(10,2) DEFAULT NULL,
 `quantity` decimal(10,2) DEFAULT NULL,
 `action_date` date DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_harvest_data	CREATE TABLE `crops_farm_harvest_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `item` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `unit_of_measure` varchar(50) DEFAULT NULL,
 `unit` decimal(10,2) DEFAULT NULL,
 `quantity` decimal(10,2) DEFAULT NULL,
 `harvest_date` date DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT 'active',
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_marketing_data	CREATE TABLE `crops_farm_marketing_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `farmer_id` int(11) NOT NULL,
 `block_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `market_date` date DEFAULT NULL,
 `market_stage` varchar(100) DEFAULT NULL,
 `buyer_id` int(11) DEFAULT NULL,
 `selling_location` varchar(255) DEFAULT NULL,
 `product` varchar(255) NOT NULL,
 `product_type` varchar(200) DEFAULT NULL,
 `description` text DEFAULT NULL,
 `unit_of_measure` varchar(50) DEFAULT NULL,
 `unit` decimal(10,2) DEFAULT NULL,
 `quantity` decimal(10,2) DEFAULT NULL,
 `market_price_per_unit` decimal(10,2) DEFAULT NULL,
 `total_freight_cost` decimal(10,2) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `status` enum('active','inactive') DEFAULT 'active',
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_pesticides_data	CREATE TABLE `crops_farm_pesticides_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `pesticide_id` int(11) NOT NULL,
 `crop_id` int(11) NOT NULL,
 `name` varchar(255) DEFAULT NULL,
 `brand` varchar(255) DEFAULT NULL,
 `unit_of_measure` varchar(50) DEFAULT NULL,
 `unit` decimal(10,2) DEFAULT NULL,
 `quantity` decimal(10,2) DEFAULT NULL,
 `action_date` date DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crops_farm_tools	CREATE TABLE `crops_farm_tools` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `tool_type` enum('general','speciality') NOT NULL,
 `crop_id` int(11) DEFAULT NULL,
 `tool_name` varchar(255) NOT NULL,
 `remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` enum('active','inactive') DEFAULT 'active',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crop_buyers	CREATE TABLE `crop_buyers` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `crop_id` int(11) NOT NULL,
 `buyer_code` varchar(50) NOT NULL,
 `name` varchar(255) NOT NULL,
 `address` text DEFAULT NULL,
 `contact_number` varchar(20) DEFAULT NULL,
 `email` varchar(100) DEFAULT NULL,
 `operation_span` enum('local','national') NOT NULL,
 `location_id` int(11) DEFAULT NULL,
 `description` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` enum('active','inactive') DEFAULT 'active',
 PRIMARY KEY (`id`),
 UNIQUE KEY `buyer_code` (`buyer_code`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
crop_processors	CREATE TABLE `crop_processors` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `crop_id` int(11) NOT NULL,
 `stage` enum('pre-harvest','harvest','post-harvest') NOT NULL,
 `processor_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_org	CREATE TABLE `dakoii_org` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `orgcode` varchar(500) NOT NULL,
 `name` varchar(255) NOT NULL,
 `description` text NOT NULL,
 `addlockprov` varchar(100) NOT NULL,
 `addlockcountry` varchar(100) NOT NULL,
 `orglogo` varchar(200) NOT NULL,
 `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
 `province_json` varchar(255) NOT NULL,
 `district_json` varchar(255) NOT NULL,
 `llg_json` varchar(255) NOT NULL,
 `is_active` tinyint(1) DEFAULT 1,
 `license_status` varchar(50) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` varchar(100) NOT NULL,
 `is_active` tinyint(1) DEFAULT 0,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
documents_folder	CREATE TABLE `documents_folder` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `parent_folder_id` int(11) DEFAULT NULL,
 `folder_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) DEFAULT NULL,
 `llg_id` int(11) DEFAULT NULL,
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
document_files	CREATE TABLE `document_files` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `folder_id` int(11) NOT NULL,
 `file_name` varchar(255) NOT NULL,
 `file_path` varchar(500) NOT NULL,
 `file_type` varchar(100) DEFAULT NULL,
 `file_size` int(11) DEFAULT NULL,
 `description` text DEFAULT NULL,
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
exercises	CREATE TABLE `exercises` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) unsigned NOT NULL,
 `country_id` int(11) unsigned NOT NULL,
 `province_id` int(11) unsigned NOT NULL,
 `district_id` int(11) unsigned NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `date_from` date NOT NULL,
 `date_to` date NOT NULL,
 `officer_responsible_id` int(11) unsigned NOT NULL,
 `status` enum('draft','active','submitted','approved','cancelled') NOT NULL DEFAULT 'draft',
 `status_at` datetime DEFAULT NULL,
 `status_by` int(11) unsigned DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `exercises_org_id_foreign` (`org_id`),
 KEY `exercises_country_id_foreign` (`country_id`),
 KEY `exercises_province_id_foreign` (`province_id`),
 KEY `exercises_district_id_foreign` (`district_id`),
 KEY `exercises_officer_responsible_id_foreign` (`officer_responsible_id`),
 KEY `exercises_status_index` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
exercise_officers	CREATE TABLE `exercise_officers` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) unsigned NOT NULL,
 `user_id` int(11) unsigned NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) unsigned NOT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `exercise_officers_exercise_id_foreign` (`exercise_id`),
 CONSTRAINT `exercise_officers_exercise_id_foreign` FOREIGN KEY (`exercise_id`) REFERENCES `exercises` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
farmers_children	CREATE TABLE `farmers_children` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `farmer_id` int(11) NOT NULL,
 `name` varchar(100) NOT NULL,
 `date_of_birth` date NOT NULL,
 `gender` enum('Male','Female') NOT NULL,
 `created_by` int(11) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
farmer_information	CREATE TABLE `farmer_information` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) DEFAULT NULL,
 `farmer_code` varchar(20) NOT NULL,
 `given_name` varchar(50) NOT NULL,
 `surname` varchar(50) NOT NULL,
 `date_of_birth` date NOT NULL,
 `gender` enum('Male','Female') NOT NULL,
 `village` varchar(100) DEFAULT NULL,
 `ward_id` int(11) DEFAULT NULL,
 `llg_id` int(11) DEFAULT NULL,
 `district_id` int(11) DEFAULT NULL,
 `province_id` int(11) DEFAULT NULL,
 `country_id` int(11) DEFAULT 1,
 `phone` varchar(200) DEFAULT NULL,
 `email` varchar(255) DEFAULT NULL,
 `address` varchar(255) DEFAULT NULL,
 `marital_status` enum('Single','Married','Divorce','Widow','De-facto') NOT NULL,
 `highest_education_id` int(11) DEFAULT NULL,
 `course_taken` varchar(255) DEFAULT NULL,
 `id_photo` varchar(255) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `status` enum('active','inactive') DEFAULT 'active',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
field_visits	CREATE TABLE `field_visits` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
 `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
 `officers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`officers`)),
 `date_start` date NOT NULL,
 `date_end` date NOT NULL,
 `purpose` text NOT NULL,
 `achievements` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`achievements`)),
 `beneficiaries` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`beneficiaries`)),
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
gov_structure	CREATE TABLE `gov_structure` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `parent_id` int(11) DEFAULT NULL,
 `json_id` varchar(255) NOT NULL,
 `level` varchar(255) NOT NULL COMMENT 'province, district, llg, ward',
 `code` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `flag_filepath` varchar(255) NOT NULL,
 `map_center` varchar(100) NOT NULL,
 `map_zoom` varchar(11) NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` varchar(255) NOT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` varchar(255) NOT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
groupings	CREATE TABLE `groupings` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `parent_id` int(11) DEFAULT NULL,
 `created_by` varchar(200) DEFAULT NULL,
 `updated_by` varchar(200) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `fk_parent_id` (`parent_id`),
 CONSTRAINT `fk_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `groupings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
inputs	CREATE TABLE `inputs` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
 `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
 `officers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`officers`)),
 `item` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `unit` varchar(50) NOT NULL,
 `quantity` decimal(10,2) NOT NULL,
 `remarks` text DEFAULT NULL,
 `recipients` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`recipients`)),
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
livestock_farm_blocks	CREATE TABLE `livestock_farm_blocks` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `farmer_id` int(11) NOT NULL,
 `block_code` varchar(50) NOT NULL,
 `org_id` int(11) NOT NULL,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `ward_id` int(11) NOT NULL,
 `village` varchar(100) NOT NULL,
 `block_site` varchar(200) NOT NULL,
 `lon` varchar(50) DEFAULT NULL,
 `lat` varchar(50) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `status` varchar(50) NOT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime NOT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
livestock_farm_data	CREATE TABLE `livestock_farm_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `block_id` int(11) NOT NULL,
 `livestock_id` int(11) NOT NULL,
 `breed` varchar(255) NOT NULL,
 `he_total` int(11) DEFAULT 0,
 `she_total` int(11) DEFAULT 0,
 `pasture_type` varchar(255) DEFAULT NULL,
 `growth_stage` varchar(255) DEFAULT NULL,
 `cost_per_livestock` varchar(20) NOT NULL,
 `low_price_per_livestock` varchar(20) NOT NULL,
 `high_price_per_livestock` varchar(20) NOT NULL,
 `comments` text DEFAULT NULL,
 `action_date` datetime DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT 'active',
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
livestock_production_data	CREATE TABLE `livestock_production_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) DEFAULT NULL,
 `livestock_id` int(11) NOT NULL,
 `item` varchar(255) NOT NULL,
 `unit_of_measure` varchar(50) NOT NULL,
 `unit` decimal(10,2) NOT NULL,
 `price_per_unit` decimal(10,2) DEFAULT NULL,
 `cost_per_unit` decimal(10,2) DEFAULT NULL,
 `quantity` int(11) DEFAULT 0,
 `clients` text DEFAULT NULL,
 `action_date` datetime DEFAULT current_timestamp(),
 `comments` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `status` varchar(50) DEFAULT 'active',
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
permissions_items	CREATE TABLE `permissions_items` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `permission_code` varchar(255) NOT NULL,
 `permission_text` text NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `created_by` varchar(255) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
permissions_sets	CREATE TABLE `permissions_sets` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `permission_id` int(11) NOT NULL,
 `user_id` int(11) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `created_by` varchar(255) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
permissions_user_districts	CREATE TABLE `permissions_user_districts` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `user_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `default_district` tinyint(1) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
settings	CREATE TABLE `settings` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `value` varchar(200) NOT NULL,
 `name` varchar(200) NOT NULL,
 `create_at` datetime NOT NULL DEFAULT current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
trainings	CREATE TABLE `trainings` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `country_id` int(11) NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
 `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
 `date_start` date NOT NULL,
 `date_end` date NOT NULL,
 `topic` varchar(255) NOT NULL,
 `objectives` text NOT NULL,
 `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`content`)),
 `trainers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`trainers`)),
 `attendees` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attendees`)),
 `materials` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`materials`)),
 `status` tinyint(1) NOT NULL DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) NOT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
users	CREATE TABLE `users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `name` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` enum('admin','supervisor','user','guest') NOT NULL DEFAULT 'user',
 `position` varchar(255) DEFAULT NULL,
 `id_photo` varchar(500) NOT NULL,
 `phone` varchar(200) NOT NULL,
 `email` varchar(500) NOT NULL,
 `status` tinyint(1) NOT NULL DEFAULT 0,
 `status_at` datetime DEFAULT NULL,
 `status_by` int(11) DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` varchar(200) DEFAULT NULL,
 `updated_by` varchar(200) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_infrastructure_activities	CREATE TABLE `workplan_infrastructure_activities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) NOT NULL,
 `infrastructure_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `location` varchar(255) NOT NULL,
 `gps_coordinates` varchar(100) DEFAULT NULL,
 `supervisor_id` int(11) DEFAULT NULL,
 `action_officer_id` int(11) DEFAULT NULL,
 `infrastructure_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`infrastructure_images`)),
 `infrastructure_status` enum('pending','submitted','approved','rated') NOT NULL DEFAULT 'pending',
 `infrastructure_status_by` int(11) DEFAULT NULL,
 `infrastructure_status_at` datetime DEFAULT NULL,
 `infrastructure_status_remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
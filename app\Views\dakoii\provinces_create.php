<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> Add New Province</h5>
                    <a href="<?= base_url('location-management/provinces') ?>" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to Provinces
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('location-management/provinces', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="form-group">
                            <label for="name">Province Name <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?= old('name') ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid province name.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="provincecode">Province Code <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="provincecode" 
                                   name="provincecode" 
                                   value="<?= old('provincecode') ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid province code.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="json_id">JSON ID</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="json_id" 
                                   name="json_id" 
                                   value="<?= old('json_id') ?>">
                            <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                        </div>

                        <!-- Hidden country field -->
                        <input type="hidden" name="country_id" value="<?= $set_country['id'] ?>">

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Province
                            </button>
                            <a href="<?= base_url('location-management/provinces') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Country:</strong> <?= esc($set_country['name']) ?></p>
                    <hr>
                    <h6>Guidelines:</h6>
                    <ul class="small">
                        <li>Province name must be at least 3 characters long</li>
                        <li>Province code must be unique</li>
                        <li>JSON ID is optional and used for mapping purposes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection(); ?>

<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Users Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Users</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="card card-success card-outline">
            <div class="card-header">
                <h3 class="card-title">User List</h3>
                <div class="card-tools">
                    <a href="<?= base_url() ?>users/create" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Add User
                    </a>
                </div>
            </div>
            <div class="card-body">
                <table id="usersTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Role</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= esc($user['name']) ?></td>
                            <td><?= esc($user['position'] ?? 'Not specified') ?></td>
                            <td>
                                <?php
                                $roleClass = [
                                    'admin' => 'danger',
                                    'supervisor' => 'warning',
                                    'user' => 'primary',
                                    'guest' => 'secondary'
                                ];
                                ?>
                                <span class="badge badge-<?= $roleClass[$user['role']] ?? 'secondary' ?>">
                                    <?= esc(ucfirst($user['role'])) ?>
                                </span>
                            </td>
                            <td><?= esc($user['email'] ?? 'Not provided') ?></td>
                            <td><?= esc($user['phone'] ?? 'Not provided') ?></td>
                            <td>
                                <span class="badge badge-<?= $user['status'] ? 'success' : 'danger' ?>">
                                    <?= $user['status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?= base_url() ?>users/view/<?= $user['id'] ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <button type="button" class="btn btn-sm btn-primary"
                                        onclick="editUser(<?= esc(json_encode($user)) ?>)">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button type="button" class="btn btn-sm btn-danger"
                                        onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>



<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('users/update_user') ?>
            <input type="hidden" name="id" id="edit_id">
            <div class="modal-body">
                <div class="form-group">
                    <label>Full Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>Position</label>
                    <input type="text" class="form-control" name="position" id="edit_position">
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" class="form-control" name="email" id="edit_email">
                </div>
                <div class="form-group">
                    <label>Phone</label>
                    <input type="tel" class="form-control" name="phone" id="edit_phone">
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_role" required>
                        <option value="admin">Administrator</option>
                        <option value="supervisor">Supervisor</option>
                        <option value="user">User</option>
                        <option value="guest">Guest</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select class="form-control" name="status" id="edit_status">
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>New Password (leave blank to keep current)</label>
                    <input type="password" class="form-control" name="password">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Update User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#usersTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": [ "excel", "pdf", "print"]
    }).buttons().container().appendTo('#usersTable_wrapper .col-md-6:eq(0)');
});

function editUser(user) {
    $('#edit_id').val(user.id);
    $('#edit_name').val(user.name);
    $('#edit_position').val(user.position || '');
    $('#edit_email').val(user.email || '');
    $('#edit_phone').val(user.phone || '');
    $('#edit_role').val(user.role);
    $('#edit_status').val(user.status);
    $('#editUserModal').modal('show');
}

function confirmDelete(id, name) {
    if (confirm('Are you sure you want to delete this user: ' + name + '?')) {
        window.location.href = '<?= base_url() ?>users/delete_user/' + id;
    }
}
</script>

<style>
.modal-header {
    color: white;
}
.badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
}
#filenoFeedback {
    margin-top: 0.25rem;
    font-size: 80%;
}
.text-success {
    color: #28a745 !important;
}
.text-danger {
    color: #dc3545 !important;
}
</style>

<?= $this->endSection() ?>
